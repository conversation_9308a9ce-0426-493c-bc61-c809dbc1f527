# Interview Submission API Documentation

## Overview
This API allows you to submit interview results for job applications. It supports three types of interviews: HR, Technical, and Final interviews. The API calculates average scores and updates the corresponding records in your Notion database.

## Base URL
- **Local Development**: `http://localhost:3000/api/applications`
- **Vercel Production**: `https://your-vercel-app.vercel.app/api/applications`

## Endpoints

### 1. Submit Interview Results
**POST** `/interview`

Submit interview results for a specific application and interview type.

#### Request Body
```json
{
  "applicationId": "string (required) - Notion page ID",
  "interviewType": "string (required) - hr|technical|final", 
  "questions": [
    {
      "question": "string (required) - The interview question",
      "score": "number (required) - Score given (0-5 or custom max)",
      "maxScore": "number (optional) - Maximum possible score (default: 5)",
      "notes": "string (optional) - Additional notes"
    }
  ],
  "comments": "string (optional) - Overall interview comments"
}
```

#### Example Request
```json
{
  "applicationId": "21b21223-e79e-81c1-9a80-efa6642f881e",
  "interviewType": "hr",
  "questions": [
    {
      "question": "Tell me about yourself",
      "score": 4,
      "maxScore": 5,
      "notes": "Good communication skills"
    },
    {
      "question": "Why do you want to work here?",
      "score": 3,
      "maxScore": 5,
      "notes": "Average response"
    }
  ],
  "comments": "Candidate showed good communication skills. Recommended for technical round."
}
```

#### Response
```json
{
  "success": true,
  "message": "Hr interview submitted successfully",
  "data": {
    "applicationId": "21b21223-e79e-81c1-9a80-efa6642f881e",
    "interviewType": "hr",
    "averageScore": 3.5,
    "totalQuestions": 2,
    "updatedAt": "2025-06-27T10:30:00.000Z"
  }
}
```

## Interview Types

### 1. HR Interview (`"hr"`)
- Updates: `HR Interview Score`, `HR Interview Comments`, `HR Interview Questions`
- Focus: Communication, cultural fit, basic qualifications

### 2. Technical Interview (`"technical"`)
- Updates: `Technical Interview Score`, `Technical Interview Comments`, `Technical Interview Questions`
- Focus: Technical skills, problem-solving, coding abilities

### 3. Final Interview (`"final"`)
- Updates: `Final Interview Score`, `Final Interview Comments`, `Final Interview Questions`
- Focus: Final assessment, salary discussion, decision making

## Score Calculation

The API automatically calculates the average score from all questions:

1. **Normalization**: All scores are normalized to a 5-point scale
   - If `maxScore` is 10 and `score` is 8, normalized score = (8/10) * 5 = 4
2. **Average**: Sum of all normalized scores divided by number of questions
3. **Rounding**: Result is rounded to 2 decimal places

### Example:
```
Question 1: 4/5 = 4.0
Question 2: 3/5 = 3.0  
Question 3: 8/10 = 4.0 (normalized)
Average: (4.0 + 3.0 + 4.0) / 3 = 3.67
```

## Error Responses

### 400 Bad Request
```json
{
  "success": false,
  "message": "Missing required fields: applicationId, interviewType, and questions are required"
}
```

### 404 Not Found
```json
{
  "success": false,
  "message": "Application not found"
}
```

### 500 Server Error
```json
{
  "success": false,
  "message": "Server error while submitting interview",
  "error": "Detailed error message"
}
```

## Usage Examples

### JavaScript/Node.js
```javascript
const axios = require('axios');

const submitInterview = async (interviewData) => {
  try {
    const response = await axios.post(
      'https://your-vercel-app.vercel.app/api/applications/interview',
      interviewData,
      {
        headers: { 'Content-Type': 'application/json' }
      }
    );
    console.log('Success:', response.data);
  } catch (error) {
    console.error('Error:', error.response.data);
  }
};
```

### cURL
```bash
curl -X POST https://your-vercel-app.vercel.app/api/applications/interview \
  -H "Content-Type: application/json" \
  -d '{
    "applicationId": "21b21223-e79e-81c1-9a80-efa6642f881e",
    "interviewType": "technical",
    "questions": [
      {
        "question": "Explain JavaScript closures",
        "score": 4,
        "maxScore": 5
      }
    ],
    "comments": "Good technical knowledge"
  }'
```

## Notion Database Updates

The API updates the following fields in your Notion database:

| Interview Type | Score Field | Comments Field | Questions Field |
|---------------|-------------|----------------|-----------------|
| HR | HR Interview Score | HR Interview Comments | HR Interview Questions |
| Technical | Technical Interview Score | Technical Interview Comments | Technical Interview Questions |
| Final | Final Interview Score | Final Interview Comments | Final Interview Questions |

## Deployment Notes

1. **Environment Variables**: Ensure `NOTION_TOKEN` and `APPLICATION_DATABASE_ID` are set
2. **CORS**: The API includes CORS support for web applications
3. **Vercel**: The API is ready for Vercel deployment with the existing `vercel.json` configuration
