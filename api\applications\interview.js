const { Client } = require('@notionhq/client');
const axios = require('axios');

// Initialize Notion client
const notion = new Client({
  auth: process.env.NOTION_TOKEN || 'ntn_q88942775343WsZKAfos9DYmAhODSKSPmPmc19L6Xhc7L1',
});

const applicationDatabaseId = process.env.APPLICATION_DATABASE_ID || '1d921223-e79e-8164-8cd4-fa013f4dd093';

// Function to calculate average score from interview questions
function calculateAverageScore(questions) {
  if (!questions || questions.length === 0) {
    return 0;
  }

  const totalScore = questions.reduce((sum, question) => {
    const score = parseFloat(question.score) || 0;
    const maxScore = parseFloat(question.maxScore) || 5;
    // Normalize to 5-point scale
    const normalizedScore = (score / maxScore) * 5;
    return sum + normalizedScore;
  }, 0);

  return Math.round((totalScore / questions.length) * 100) / 100; // Round to 2 decimal places
}

// Function to update application record in Notion
async function updateApplicationInNotion(applicationId, updateData) {
  try {
    const response = await notion.pages.update({
      page_id: applicationId,
      properties: updateData
    });

    return response;
  } catch (error) {
    console.error('Error updating application in Notion:', error);
    
    // Fallback to axios
    try {
      const url = `https://api.notion.com/v1/pages/${applicationId}`;
      const headers = {
        'Authorization': `Bearer ${process.env.NOTION_TOKEN || 'ntn_q88942775343WsZKAfos9DYmAhODSKSPmPmc19L6Xhc7L1'}`,
        'Content-Type': 'application/json',
        'Notion-Version': '2022-06-28'
      };

      const response = await axios.patch(url, { properties: updateData }, { headers });
      return response.data;
    } catch (axiosError) {
      console.error('Axios fallback error:', axiosError);
      throw axiosError;
    }
  }
}

// Main serverless function
module.exports = async (req, res) => {
  // Enable CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      message: 'Method not allowed. Use POST.'
    });
  }

  try {
    const { applicationId, interviewType, questions, comments } = req.body;

    // Validate required fields
    if (!applicationId || !interviewType || !questions) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: applicationId, interviewType, and questions are required'
      });
    }

    // Validate interview type
    const validInterviewTypes = ['hr', 'technical', 'final'];
    if (!validInterviewTypes.includes(interviewType.toLowerCase())) {
      return res.status(400).json({
        success: false,
        message: 'Invalid interview type. Must be one of: hr, technical, final'
      });
    }

    // Validate questions format
    if (!Array.isArray(questions) || questions.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Questions must be a non-empty array'
      });
    }

    // Validate each question has required fields
    for (let i = 0; i < questions.length; i++) {
      const question = questions[i];
      if (!question.question || question.score === undefined) {
        return res.status(400).json({
          success: false,
          message: `Question ${i + 1} must have 'question' and 'score' fields`
        });
      }
      
      const score = parseFloat(question.score);
      const maxScore = parseFloat(question.maxScore) || 5;
      
      if (isNaN(score) || score < 0 || score > maxScore) {
        return res.status(400).json({
          success: false,
          message: `Question ${i + 1} score must be a number between 0 and ${maxScore}`
        });
      }
    }

    // Calculate average score
    const averageScore = calculateAverageScore(questions);

    // Prepare update data based on interview type
    const updateData = {};
    const interviewTypeKey = interviewType.toLowerCase();

    // Map interview types to Notion property names
    const propertyMapping = {
      'hr': {
        interviewField: 'HR Interview'
      },
      'technical': {
        interviewField: 'Technical Interview'
      },
      'final': {
        interviewField: 'Final Interview'
      }
    };

    const mapping = propertyMapping[interviewTypeKey];

    // Create interview data object
    const interviewData = {
      questions: questions,
      averageScore: averageScore,
      comments: comments || '',
      submittedAt: new Date().toISOString(),
      totalQuestions: questions.length
    };

    // Update the interview field with complete JSON data
    updateData[mapping.interviewField] = {
      rich_text: [
        {
          type: 'text',
          text: {
            content: JSON.stringify(interviewData, null, 2)
          }
        }
      ]
    };

    // Update the record in Notion
    await updateApplicationInNotion(applicationId, updateData);

    console.log(`Successfully updated ${interviewType} interview for application ${applicationId} with score ${averageScore}`);

    res.json({
      success: true,
      message: `${interviewType.charAt(0).toUpperCase() + interviewType.slice(1)} interview submitted successfully`,
      data: {
        applicationId,
        interviewType,
        averageScore,
        totalQuestions: questions.length,
        updatedAt: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Error in submitInterview:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while submitting interview',
      error: error.message
    });
  }
};
