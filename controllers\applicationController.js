const { Client } = require('@notionhq/client');
const axios = require('axios');

// Initialize Notion client
const notion = new Client({
  auth: process.env.NOTION_TOKEN || 'ntn_q88942775343WsZKAfos9DYmAhODSKSPmPmc19L6Xhc7L1'
});
const applicationDatabaseId =
  process.env.APPLICATION_DATABASE_ID || "1d921223-e79e-8164-8cd4-fa013f4dd093";


// Function to get all application records from Notion with pagination
async function getApplicationsRecordsFromNotion() {
  try {
    let allResults = [];
    let hasMore = true;
    let startCursor = undefined;

    // Keep fetching pages until we have all records
    while (hasMore) {
      const queryParams = {
        database_id: applicationDatabaseId,
        page_size: 100 // Maximum allowed by Notion API
      };

      // Add start_cursor for pagination (except for first request)
      if (startCursor) {
        queryParams.start_cursor = startCursor;
      }

      const response = await notion.databases.query(queryParams);

      if (response && response.results) {
        allResults = allResults.concat(response.results);
        hasMore = response.has_more;
        startCursor = response.next_cursor;

        console.log(`Fetched ${response.results.length} records. Total so far: ${allResults.length}`);
      } else {
        return { error: 'Failed to retrieve application records' };
      }
    }

    console.log(`Successfully retrieved all ${allResults.length} application records from Notion`);
    return allResults;

  } catch (error) {
    console.error('Error fetching from Notion:', error);

    // Fallback to using axios if the Notion client fails
    try {
      let allResults = [];
      let hasMore = true;
      let startCursor = undefined;

      while (hasMore) {
        const url = `https://api.notion.com/v1/databases/${applicationDatabaseId}/query`;
        const headers = {
          'Authorization': `Bearer ${process.env.NOTION_TOKEN || 'ntn_q88942775343WsZKAfos9DYmAhODSKSPmPmc19L6Xhc7L1'}`,
          'Content-Type': 'application/json',
          'Notion-Version': '2022-06-28'
        };

        const requestBody = {
          page_size: 100
        };

        if (startCursor) {
          requestBody.start_cursor = startCursor;
        }

        const response = await axios.post(url, requestBody, { headers });

        if (response.data && response.data.results) {
          allResults = allResults.concat(response.data.results);
          hasMore = response.data.has_more;
          startCursor = response.data.next_cursor;

          console.log(`Axios fallback: Fetched ${response.data.results.length} records. Total so far: ${allResults.length}`);
        } else {
          break;
        }
      }

      if (allResults.length > 0) {
        console.log(`Axios fallback: Successfully retrieved all ${allResults.length} application records`);
        return allResults;
      }
    } catch (axiosError) {
      console.error('Axios fallback error:', axiosError);
    }

    return { error: 'Failed to retrieve application records' };
  }
}

// Get applications (all or by ID)
exports.getApplications = async (req, res) => {
  try {
    // Get the 'id' query parameter from the URL
    const requestedId = req.query.id || null;

    const allRecords = await getApplicationsRecordsFromNotion();

    console.log(`Total records retrieved: ${allRecords.length}`);

    if (allRecords.error) {
      return res.status(500).json({ success: false, message: allRecords.error });
    }



    // If an ID is present in the query string, find and return only that record
    if (requestedId) {
      const record = allRecords.find(record => record.id === requestedId);

      if (!record) {
        return res.status(404).json({ error: 'Record not found' });
      }

      return res.json(record);
    }

    // No specific ID, return all records
    res.json(allRecords);
  } catch (error) {
    console.error('Error in getApplications:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
};



// Function to calculate average score from interview questions
function calculateAverageScore(questions) {
  if (!questions || questions.length === 0) {
    return 0;
  }

  const totalScore = questions.reduce((sum, question) => {
    const score = parseFloat(question.score) || 0;
    const maxScore = parseFloat(question.maxScore) || 5;
    // Normalize to 5-point scale
    const normalizedScore = (score / maxScore) * 5;
    return sum + normalizedScore;
  }, 0);

  return Math.round((totalScore / questions.length) * 100) / 100; // Round to 2 decimal places
}

// Function to update application record in Notion
async function updateApplicationInNotion(applicationId, updateData) {
  try {
    const response = await notion.pages.update({
      page_id: applicationId,
      properties: updateData
    });

    return response;
  } catch (error) {
    console.error('Error updating application in Notion:', error);

    // Fallback to axios
    try {
      const url = `https://api.notion.com/v1/pages/${applicationId}`;
      const headers = {
        'Authorization': `Bearer ${process.env.NOTION_TOKEN || 'ntn_q88942775343WsZKAfos9DYmAhODSKSPmPmc19L6Xhc7L1'}`,
        'Content-Type': 'application/json',
        'Notion-Version': '2022-06-28'
      };

      const response = await axios.patch(url, { properties: updateData }, { headers });
      return response.data;
    } catch (axiosError) {
      console.error('Axios fallback error:', axiosError);
      throw axiosError;
    }
  }
}

// Submit interview results (HR, Technical, or Final)
exports.submitInterview = async (req, res) => {
  try {
    const { applicationId, interviewType, questions, comments } = req.body;

    // Validate required fields
    if (!applicationId || !interviewType || !questions) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: applicationId, interviewType, and questions are required'
      });
    }

    // Validate interview type
    const validInterviewTypes = ['hr', 'technical', 'final'];
    if (!validInterviewTypes.includes(interviewType.toLowerCase())) {
      return res.status(400).json({
        success: false,
        message: 'Invalid interview type. Must be one of: hr, technical, final'
      });
    }

    // Validate questions format
    if (!Array.isArray(questions) || questions.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Questions must be a non-empty array'
      });
    }

    // Validate each question has required fields
    for (let i = 0; i < questions.length; i++) {
      const question = questions[i];
      if (!question.question || question.score === undefined) {
        return res.status(400).json({
          success: false,
          message: `Question ${i + 1} must have 'question' and 'score' fields`
        });
      }

      const score = parseFloat(question.score);
      const maxScore = parseFloat(question.maxScore) || 5;

      if (isNaN(score) || score < 0 || score > maxScore) {
        return res.status(400).json({
          success: false,
          message: `Question ${i + 1} score must be a number between 0 and ${maxScore}`
        });
      }
    }

    // Calculate average score
    const averageScore = calculateAverageScore(questions);

    // Prepare update data based on interview type
    const updateData = {};
    const interviewTypeKey = interviewType.toLowerCase();

    // Map interview types to Notion property names (using actual property names from your database)
    const propertyMapping = {
      'hr': {
        interviewField: 'HR Interview'
      },
      'technical': {
        interviewField: 'Technical Interview'
      },
      'final': {
        interviewField: 'Final Interview'
      }
    };

    const mapping = propertyMapping[interviewTypeKey];

    // Create interview data object
    const interviewData = {
      questions: questions,
      averageScore: averageScore,
      comments: comments || '',
      submittedAt: new Date().toISOString(),
      totalQuestions: questions.length
    };

    // Update the interview field with complete JSON data
    updateData[mapping.interviewField] = {
      rich_text: [
        {
          type: 'text',
          text: {
            content: JSON.stringify(interviewData, null, 2)
          }
        }
      ]
    };

    // Update the record in Notion
    await updateApplicationInNotion(applicationId, updateData);

    console.log(`Successfully updated ${interviewType} interview for application ${applicationId} with score ${averageScore}`);

    res.json({
      success: true,
      message: `${interviewType.charAt(0).toUpperCase() + interviewType.slice(1)} interview submitted successfully`,
      data: {
        applicationId,
        interviewType,
        averageScore,
        totalQuestions: questions.length,
        updatedAt: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Error in submitInterview:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while submitting interview',
      error: error.message
    });
  }
};

