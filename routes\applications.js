const express = require("express");
const router = express.Router();
const applicationController = require("../controllers/applicationController");

// GET /api/applications (with optional id query parameter)
router.get('/', applicationController.getApplications);

// GET /api/applications/properties - Get property names for debugging
router.get('/properties', applicationController.getProperties);

// POST /api/applications/interview - Submit interview results
router.post('/interview', applicationController.submitInterview);

module.exports = router;
